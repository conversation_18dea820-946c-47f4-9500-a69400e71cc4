# AI服务（ai-service）API接口文档

## 1. 概述
AI服务负责图像识别、风格分析、色彩分析、AI对话、视频生成、形象评估、AI评分等智能能力，为衣橱、推荐等业务提供AI支撑。

## 2. 认证与通用说明
- 认证方式：JWT（登录成功后在响应header中返回，后续接口需在请求header中携带Authorization: Bearer {token}）
- 部分接口可开放访问，部分需要认证
- 通用请求头：
  - Content-Type: application/json
  - Authorization: Bearer {token} （需要认证的接口）
- 通用响应结构：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": { ... }
    }
    ```
- 错误码说明：
    | code | 含义         | 说明           |
    |------|--------------|----------------|
    | 0    | 成功         |                |
    | 1001 | 参数错误     |                |
    | 1002 | 未认证/Token无效 |           |
    | 1003 | 权限不足     |                |
    | 6001 | AI服务异常   |                |
    | 6002 | 图片格式不支持 |              |
    | 6003 | 非正面全身照 |                |
    | ...  | ...          |                |

## 3. 接口列表

### 3.1 图像识别
- **接口路径**：`/api/ai/recognize`
- **请求方法**：POST
- **功能描述**：识别衣物/饰品类别、颜色、风格等
- **请求参数**：
    | 参数名 | 类型      | 必填 | 说明     |
    |--------|-----------|------|----------|
    | file   | multipart | 是   | 图片文件 |
    | type   | string    | 否   | 识别类型（clothing/accessory/）|
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | category | string | 类别         |
    | colors   | array  | 颜色         |
    | style    | string | 风格         |
    | ...      | ...    | ...          |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "category": "上衣",
        "colors": ["白色"],
        "style": "休闲"
      }
    }
    ```

---

### 3.2 全身照身材识别（直接上传文件）
- **接口路径**：`/api/ai/analyze-body-shape`
- **请求方法**：POST
- **功能描述**：直接上传图片文件进行全身照识别和身材分析
- **请求参数**：
    | 参数名        | 类型      | 必填 | 说明                                           |
    |---------------|-----------|------|------------------------------------------------|
    | file          | multipart | 是   | 图片文件（支持jpg、png等格式）                  |
    | analysisType  | string    | 否   | 分析类型（默认为"full"）                       |
- **响应参数**：同3.3接口

### 3.3 全身照身材识别（通过文件ID）
- **接口路径**：`/api/ai/analyze-body-shape-by-file-id`
- **请求方法**：POST
- **功能描述**：通过已上传的文件ID进行全身照识别和身材分析
- **请求参数**：
    | 参数名        | 类型   | 必填 | 说明                                           |
    |---------------|--------|------|------------------------------------------------|
    | fileId        | string | 是   | 文件服务返回的图片文件ID                       |
    | analysisType  | string | 否   | 分析类型（默认为"full"）                       |
- **响应参数**：
    | 字段名           | 类型    | 说明                                   |
    |------------------|---------|----------------------------------------|
    | fullBodyPhoto    | boolean | 是否为正面全身照                       |
    | confidence       | number  | 识别置信度（0-100）                    |
    | bodyType         | string  | 体型分类（如"沙漏形"、"梨形"等）       |
    | bodyShape        | object  | 详细身材数据，见下表                   |
    | analysis         | array   | 身材分析结果                           |
    | suggestions      | array   | 穿搭建议                               |
    | reason           | string  | 识别失败原因（可为null）               |
    | timestamp        | number  | 时间戳                                 |

- **bodyShape详细字段**：
    | 字段名        | 类型   | 说明       | 可选值 | 前端映射说明                                |
    |---------------|--------|------------|--------|---------------------------------------------|
    | shoulderWidth | number | 肩膀宽度   | 1-5    | 1:窄, 2:偏窄, 3:正常, 4:偏宽, 5:宽          |
    | waistShape    | number | 腰型       | 1-5    | 1:直筒, 2:略有曲线, 3:有曲线, 4:曲线较明显, 5:曲线明显 |
    | belly         | number | 肚腩       | 1-5    | 1:没有, 2:略有小肚腩, 3:小肚腩, 4:偏大肚腩, 5:大肚腩 |
    | hip           | number | 臀型       | 1-5    | 1:下榻, 2:略有上翘, 3:正常, 4:较上翘, 5:上翘 |
    | hipShape      | number | 胯型       | 1-5    | 1:直筒, 2:略有曲线, 3:有曲线, 4:曲线较明显, 5:曲线明显 |
    | armLength     | number | 臂长       | 1-5    | 1:短, 2:偏短, 3:正常, 4:偏长, 5:长          |
    | armCircum     | number | 臂围       | 1-5    | 1:细, 2:偏细, 3:正常, 4:偏粗, 5:粗          |
    | hipWidth      | number | 胯部宽度   | 1-5    | 1:窄, 2:偏窄, 3:正常, 4:偏宽, 5:宽          |
    | thigh         | number | 大腿       | 1-5    | 1:细, 2:偏细, 3:正常, 4:偏粗, 5:粗          |
    | calf          | number | 小腿       | 1-5    | 1:细, 2:偏细, 3:正常, 4:偏粗, 5:粗          |
    | bodyFat       | number | 上下身粗细 | 1-5    | 1:上身粗, 2:偏上身粗, 3:匀称, 4:偏下身粗, 5:下身粗 |
    | bodyLength    | number | 上下身长短 | 1-5    | 1:上身长, 2:偏上身长, 3:匀称, 4:偏下身长, 5:下身长 |

- **analysis数组字段**：
    | 字段名      | 类型   | 说明                     |
    |-------------|--------|--------------------------|
    | feature     | string | 分析特征点               |
    | description | string | 特征描述                 |
    | type        | string | 类型（positive/neutral） |

- **suggestions数组字段**：
    | 字段名      | 类型   | 说明         |
    |-------------|--------|--------------|
    | category    | string | 建议类别     |
    | content     | string | 建议内容     |
    | priority    | string | 优先级       |

- **响应示例**：
    ```json
    {
      "code": 0,
      "message": "success",
      "data": {
        "fullBodyPhoto": true,
        "confidence": 90.0,
        "bodyType": "沙漏形",
        "bodyShape": {
          "shoulderWidth": 3,
          "waistShape": 4,
          "belly": 1,
          "hip": 4,
          "hipShape": 4,
          "armLength": 3,
          "armCircum": 2,
          "hipWidth": 3,
          "thigh": 3,
          "calf": 2,
          "bodyFat": 3,
          "bodyLength": 3
        },
        "analysis": [
          {
            "feature": "整体身材",
            "description": "整体身材较为匀称，腰臀曲线明显，四肢纤细",
            "type": "positive"
          }
        ],
        "suggestions": [
          {
            "category": "身材分析",
            "content": "拥有明显的沙漏形身材特征，腰臀比例优秀，整体体态良好",
            "priority": "high"
          },
          {
            "category": "穿搭建议",
            "content": "可继续选择凸显腰线的服装，比如收腰连衣裙、高腰裤等，以突出优势；也可选择简约款式，减少繁杂装饰，展现优雅气质。",
            "priority": "high"
          }
        ],
        "reason": null,
        "timestamp": 1749088349705
      }
    }
    ```

- **错误响应示例**：
    ```json
    {
      "code": 6003,
      "message": "非正面全身照",
      "data": {
        "fullBodyPhoto": false,
        "confidence": 30.2,
        "reason": "图片中人物非正面朝向或非全身照",
        "timestamp": 1749088349705
      }
    }
    ```

---

### 3.4 风格分析
- **接口路径**：`/api/ai/style`
- **请求方法**：POST
- **功能描述**：分析图片或搭配的风格分布
- **请求参数**：图片或搭配ID等
- **响应参数**：风格分布、主导风格等
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "styleDistribution": {"sporty": 0.5, "casual": 0.5},
        "dominantColors": ["白色", "黑色"]
      }
    }
    ```

---

### 3.5 色彩分析
- **接口路径**：`/api/ai/color`
- **请求方法**：POST
- **功能描述**：分析图片主色调、色彩搭配
- **请求参数/响应参数/示例**：同3.2

---

### 3.6 AI对话/时尚建议
- **接口路径**：`/api/ai/chat`
- **请求方法**：POST
- **功能描述**：与AI对话，获取时尚建议
- **请求参数**：
    | 参数名 | 类型   | 必填 | 说明     |
    |--------|--------|------|----------|
    | message| string | 是   | 用户输入 |
- **响应参数**：
    | 字段名 | 类型   | 说明         |
    |--------|--------|--------------|
    | reply  | string | AI回复       |
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "reply": "建议今日选择浅色系休闲装，适合晴朗天气。"
      }
    }
    ```

---

### 3.7 视频生成
- **接口路径**：`/api/ai/video`
- **请求方法**：POST
- **功能描述**：根据搭配生成短视频
- **请求参数**：搭配ID、模板ID等
- **响应参数**：视频URL、任务状态等
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "videoUrl": "https://xxx.com/video.mp4",
        "status": "PROCESSING"
      }
    }
    ```

---

### 3.8 形象评估
- **接口路径**：`/api/ai/appearance`
- **请求方法**：POST
- **功能描述**：对用户形象或搭配进行AI评估
- **请求参数**：图片、搭配ID等
- **响应参数**：评估分数、建议等
- **响应示例**：
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "score": 85,
        "suggestion": "整体风格协调，建议增加亮色点缀。"
      }
    }
    ```

---

### 3.9 AI评分
- **接口路径**：`/api/ai/rate`
- **请求方法**：POST
- **功能描述**：对搭配、推荐等进行AI评分
- **请求参数/响应参数/示例**：同3.8

---

## 4. 典型业务流程示例
- 上传图片 -> 图像识别/风格分析 -> 获取AI建议/评分 -> 生成视频

---

## 5. 变更历史
- 2025-01-24 更新体型识别接口响应格式：fullBodyPhoto、bodyType、reason、timestamp等字段调整 - AI
- 2025-01-24 将体型识别拆分为两个接口：直接上传文件和通过文件ID - AI
- 2025-05-12 初稿 by AI 