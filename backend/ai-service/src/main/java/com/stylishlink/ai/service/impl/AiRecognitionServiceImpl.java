package com.stylishlink.ai.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stylishlink.ai.client.AiClient;
import com.stylishlink.ai.client.AiClientFactory;
import com.stylishlink.ai.dto.request.RecognizeRequest;
import com.stylishlink.ai.dto.response.ClothingItem;
import com.stylishlink.ai.dto.response.RecognitionResponse;
import com.stylishlink.ai.entity.AiRecognitionResult;
import com.stylishlink.ai.mapper.AiRecognitionResultMapper;
import com.stylishlink.ai.service.AiRecognitionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 图像识别服务实现
 */
@Slf4j
@Service
public class AiRecognitionServiceImpl implements AiRecognitionService {

    @Autowired
    private AiClientFactory aiClientFactory;

    @Autowired
    private AiRecognitionResultMapper recognitionResultMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public RecognitionResponse recognizeClothing(RecognizeRequest request) {
        request.setType("clothing");
        return recognize(request);
    }

    @Override
    public RecognitionResponse recognizeAccessory(RecognizeRequest request) {
        request.setType("accessory");
        return recognize(request);
    }

    @Override
    public RecognitionResponse recognize(RecognizeRequest request) {
        try {
            // 获取视觉专用AI客户端（豆包）进行图像识别
            AiClient visionClient = aiClientFactory.getVisionClient();
            log.info("使用视觉AI客户端进行图像识别，用户: {}", request.getUserId());

            // 调用AI服务进行识别
            String recognitionType = StringUtils.hasText(request.getType()) ? request.getType() : "clothing";
            Map<String, Object> aiResult = visionClient.recognizeImage(request.getFile(), recognitionType);
            
            log.info("AI服务返回结果: {}", aiResult);

            // 先解析aiResponse字符串
            Map<String, Object> parsedAiResponse = parseAiResponse(aiResult);
            log.debug("解析后的AI响应数据: {}", parsedAiResponse);

            // 构建响应对象
            RecognitionResponse response = new RecognitionResponse();
            
            // 尝试解析新格式的多物件识别结果
            if (parseMultiItemResponse(parsedAiResponse, response)) {
                log.info("成功解析新格式的多物件识别结果");
            } else {
                // 回退到兼容性模式，解析旧格式
                log.info("回退到兼容性模式，解析旧格式结果");
                parseLegacyResponse(parsedAiResponse, response);
            }

            // 保存识别结果到数据库
            try {
                log.debug("=== 开始保存识别结果到数据库 ===");
                
                AiRecognitionResult entity = new AiRecognitionResult();
                entity.setUserId(request.getUserId());
                // 直接使用豆包上传后返回的真实图片URL
                String imageUrl = (String) aiResult.get("imageUrl");
                entity.setImageUrl(imageUrl);
                entity.setRecognitionType(recognitionType);
                
                // 保存新格式的多物件识别结果，使用第一个物件的信息作为主记录
                if (response.isHasClothingOrAccessory() && response.getItems() != null && !response.getItems().isEmpty()) {
                    ClothingItem firstItem = response.getItems().get(0);
                    entity.setCategory(firstItem.getCategory());
                    entity.setColors(firstItem.getColors());
                    entity.setStyle(firstItem.getStyle());
                    entity.setMaterials(firstItem.getMaterials());
                    entity.setPatterns(firstItem.getPatterns());
                    entity.setConfidence(firstItem.getConfidence());
                }
                
                entity.setRawResult(objectMapper.writeValueAsString(aiResult));
                entity.setCreatedAt(LocalDateTime.now());
                entity.setUpdatedAt(LocalDateTime.now());

                log.debug("准备保存的实体数据: {}", entity);
                
                log.debug("调用 recognitionResultMapper.insert() 方法");
                int insertResult = recognitionResultMapper.insert(entity);
                log.debug("数据库插入结果: {}, 生成的ID: {}", insertResult, entity.getId());
                
                log.info("图像识别完成，用户: {}, 类型: {}, 结果ID: {}, 识别到物件数: {}", 
                        request.getUserId(), recognitionType, entity.getId(), response.getItemCount());
                        
            } catch (Exception dbException) {
                log.error("=== 保存识别结果到数据库失败 ===");
                log.error("异常类型: {}", dbException.getClass().getSimpleName());
                log.error("异常信息: {}", dbException.getMessage());
                log.error("详细堆栈:", dbException);
                
                log.warn("保存识别结果到数据库失败，但识别成功: {}", dbException.getMessage());
                // 数据库失败不影响识别结果返回
            }

            return response;

        } catch (Exception e) {
            log.error("图像识别失败，用户: {}, 类型: {}", request.getUserId(), request.getType(), e);
            throw new RuntimeException("图像识别失败: " + e.getMessage());
        }
    }

    /**
     * 解析新格式的多物件识别响应
     * @param parsedAiResponse 解析后的AI响应
     * @param response 要填充的响应对象
     * @return 是否成功解析新格式
     */
    private boolean parseMultiItemResponse(Map<String, Object> parsedAiResponse, RecognitionResponse response) {
        if (parsedAiResponse == null || parsedAiResponse.isEmpty()) {
            return false;
        }
        
        try {
            // 检查是否包含新格式的必需字段
            if (!parsedAiResponse.containsKey("hasClothingOrAccessory")) {
                return false;
            }
            
            // 解析是否识别到衣物配饰
            Object hasClothingObj = parsedAiResponse.get("hasClothingOrAccessory");
            boolean hasClothing = false;
            if (hasClothingObj instanceof Boolean) {
                hasClothing = (Boolean) hasClothingObj;
            } else if (hasClothingObj instanceof String) {
                hasClothing = "true".equalsIgnoreCase((String) hasClothingObj);
            }
            
            response.setHasClothingOrAccessory(hasClothing);
            
            if (!hasClothing) {
                // 没有识别到衣物配饰
                response.setItemCount(0);
                response.setItems(new ArrayList<>());
                log.info("AI响应：未识别到衣物或配饰");
                return true;
            }
            
            // 解析物件数量
            Object itemCountObj = parsedAiResponse.get("itemCount");
            int itemCount = 0;
            if (itemCountObj instanceof Number) {
                itemCount = ((Number) itemCountObj).intValue();
            } else if (itemCountObj instanceof String) {
                try {
                    itemCount = Integer.parseInt((String) itemCountObj);
                } catch (NumberFormatException e) {
                    log.warn("无法解析itemCount: {}", itemCountObj);
                }
            }
            response.setItemCount(itemCount);
            
            // 解析物件列表
            List<ClothingItem> items = parseClothingItems(parsedAiResponse);
            response.setItems(items);
            
            log.debug("解析物件列表完成，共{}件物品", items.size());
            for (int i = 0; i < items.size(); i++) {
                ClothingItem item = items.get(i);
                log.debug("物件[{}]: 类型={}, 类别={}, 颜色={}, 材质={}, 图案={}", 
                        i, item.getType(), item.getCategory(), 
                        item.getColors(), item.getMaterials(), item.getPatterns());
            }
            
            log.info("成功解析新格式响应，识别到{}件物品", items.size());
            return true;
            
        } catch (Exception e) {
            log.error("解析新格式响应失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 解析衣物物件列表
     */
    private List<ClothingItem> parseClothingItems(Map<String, Object> parsedAiResponse) {
        List<ClothingItem> items = new ArrayList<>();
        
        Object itemsObj = parsedAiResponse.get("items");
        if (!(itemsObj instanceof List)) {
            log.warn("items字段不是列表类型: {}", itemsObj);
            return items;
        }
        
        List<?> itemsList = (List<?>) itemsObj;
        for (Object itemObj : itemsList) {
            if (!(itemObj instanceof Map)) {
                log.warn("物件项不是Map类型: {}", itemObj);
                continue;
            }
            
            Map<String, Object> itemMap = (Map<String, Object>) itemObj;
            ClothingItem item = new ClothingItem();
            
            item.setType(extractStringValue(itemMap, "type", "未知类型"));
            item.setCategory(extractStringValue(itemMap, "category", "未知类别"));
            item.setStyle(extractStringValue(itemMap, "style", "通用风格"));
            item.setColors(extractStringList(itemMap, "colors", Arrays.asList("未知颜色")));
            item.setMaterials(extractStringList(itemMap, "materials", Arrays.asList("未知材质")));
            item.setPatterns(extractStringList(itemMap, "patterns", Arrays.asList("无图案")));
            item.setConfidence(extractConfidenceValue(itemMap));
            
            items.add(item);
            log.debug("解析到物件: 类型={}, 类别={}, 置信度={}, 材质数量={}, 图案数量={}", 
                    item.getType(), item.getCategory(), item.getConfidence(), 
                    item.getMaterials().size(), item.getPatterns().size());
        }
        
        return items;
    }
    
    /**
     * 解析旧格式响应（向后兼容）
     */
    private void parseLegacyResponse(Map<String, Object> parsedAiResponse, RecognitionResponse response) {
        // 设置新格式字段的默认值
        response.setHasClothingOrAccessory(true); // 假设旧格式都是识别到了物件
        response.setItemCount(1); // 旧格式默认为1个物件
        
        // 构建兼容性物件
        ClothingItem legacyItem = new ClothingItem();
        legacyItem.setType("衣物"); // 默认类型
        legacyItem.setCategory(extractStringValue(parsedAiResponse, "category", "未知类别"));
        legacyItem.setStyle(extractStringValue(parsedAiResponse, "style", "通用风格"));
        legacyItem.setColors(extractStringList(parsedAiResponse, "colors", Arrays.asList("未知颜色")));
        legacyItem.setMaterials(extractStringList(parsedAiResponse, "materials", Arrays.asList("未知材质")));
        legacyItem.setPatterns(extractStringList(parsedAiResponse, "patterns", Arrays.asList("无图案")));
        legacyItem.setConfidence(extractConfidenceValue(parsedAiResponse));
        
        response.setItems(Arrays.asList(legacyItem));
        
        log.info("使用兼容模式解析旧格式响应");
    }

    /**
     * 解析AI响应字符串
     */
    private Map<String, Object> parseAiResponse(Map<String, Object> aiResult) {
        if (aiResult == null || !aiResult.containsKey("aiResponse")) {
            log.warn("AI结果中不包含aiResponse字段");
            return Collections.emptyMap();
        }
        
        String aiResponseStr = (String) aiResult.get("aiResponse");
        if (aiResponseStr == null || aiResponseStr.trim().isEmpty()) {
            log.warn("AI响应内容为空");
            return Collections.emptyMap();
        }
        
        try {
            log.debug("正在解析AI响应字符串: {}", aiResponseStr);
            return objectMapper.readValue(aiResponseStr, Map.class);
        } catch (JsonProcessingException e) {
            log.error("解析AI响应字符串失败: {}", e.getMessage());
            log.debug("原始AI响应字符串: {}", aiResponseStr);
            return Collections.emptyMap();
        }
    }

    /**
     * 安全提取字符串值
     */
    private String extractStringValue(Map<String, Object> map, String key, String defaultValue) {
        if (map == null || !map.containsKey(key)) {
            return defaultValue;
        }
        Object value = map.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 安全提取置信度值
     */
    private BigDecimal extractConfidenceValue(Map<String, Object> map) {
        if (map == null || !map.containsKey("confidence")) {
            return new BigDecimal("0.5"); // 默认置信度50%
        }
        Object value = map.get("confidence");
        if (value == null) {
            return new BigDecimal("0.5");
        }
        try {
            if (value instanceof Number) {
                return BigDecimal.valueOf(((Number) value).doubleValue());
            }
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法解析置信度值: {}, 使用默认值", value);
            return new BigDecimal("0.5");
        }
    }

    /**
     * 安全提取字符串列表
     * 区分空数组和缺失字段：
     * - 如果字段不存在或为null，使用默认值
     * - 如果字段存在但为空数组，保持空数组
     */
    private List<String> extractStringList(Map<String, Object> map, String key, List<String> defaultValue) {
        if (map == null || !map.containsKey(key)) {
            // 字段不存在，使用默认值
            return defaultValue;
        }
        Object value = map.get(key);
        if (value == null) {
            // 字段存在但为null，使用默认值
            return defaultValue;
        }
        
        try {
            if (value instanceof String[]) {
                return Arrays.asList((String[]) value);
            } else if (value instanceof List) {
                List<?> list = (List<?>) value;
                // 如果是空List，保持空List（不使用默认值）
                if (list.isEmpty()) {
                    return new ArrayList<>();
                }
                List<String> stringList = new ArrayList<>();
                for (Object item : list) {
                    if (item != null) {
                        stringList.add(item.toString());
                    }
                }
                return stringList;
            } else if (value instanceof String) {
                // 尝试解析逗号分隔的字符串
                String str = value.toString().trim();
                if (str.isEmpty()) {
                    // 空字符串保持为空数组
                    return new ArrayList<>();
                }
                return Arrays.asList(str.split(","));
            }
        } catch (Exception e) {
            log.warn("无法解析{}字段: {}, 使用默认值", key, value);
        }
        
        // 无法解析的情况，使用默认值
        return defaultValue;
    }

} 