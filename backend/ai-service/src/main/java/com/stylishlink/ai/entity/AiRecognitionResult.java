package com.stylishlink.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 图像识别结果实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_recognition_result")
public class AiRecognitionResult {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 图片URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 识别类型(clothing/accessory)
     */
    @TableField("recognition_type")
    private String recognitionType;

    /**
     * 类别
     */
    @TableField("category")
    private String category;

    /**
     * 颜色列表
     */
    @TableField(value = "colors", typeHandler = JacksonTypeHandler.class)
    private List<String> colors;

    /**
     * 风格
     */
    @TableField("style")
    private String style;

    /**
     * 材质列表
     */
    @TableField(value = "materials", typeHandler = JacksonTypeHandler.class)
    private List<String> materials;

    /**
     * 图案列表
     */
    @TableField(value = "patterns", typeHandler = JacksonTypeHandler.class)
    private List<String> patterns;

    /**
     * 置信度
     */
    @TableField("confidence")
    private BigDecimal confidence;

    /**
     * 原始识别结果
     */
    @TableField("raw_result")
    private String rawResult;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
} 