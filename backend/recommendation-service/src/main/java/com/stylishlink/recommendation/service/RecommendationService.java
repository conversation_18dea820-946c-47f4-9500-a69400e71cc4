package com.stylishlink.recommendation.service;

import com.stylishlink.recommendation.dto.request.OutfitRecommendationRequest;
import com.stylishlink.recommendation.dto.response.OutfitRecommendationResponse;

import java.util.List;

/**
 * 推荐服务接口
 */
public interface RecommendationService {
    
    /**
     * 获取穿搭推荐
     * @param request 推荐请求
     * @return 推荐搭配列表
     */
    List<OutfitRecommendationResponse> getOutfitRecommendations(OutfitRecommendationRequest request);
    
    /**
     * 根据场合获取穿搭推荐
     * @param userId 用户ID
     * @param occasion 场合
     * @param count 推荐数量
     * @return 推荐搭配列表
     */
    List<OutfitRecommendationResponse> getOutfitRecommendationsByOccasion(String userId, String occasion, Integer count);
    
    /**
     * 根据天气获取穿搭推荐
     * @param userId 用户ID
     * @param city 城市
     * @param count 推荐数量
     * @return 推荐搭配列表
     */
    List<OutfitRecommendationResponse> getOutfitRecommendationsByWeather(String userId, String city, Integer count);
    
    /**
     * 根据五行命理获取穿搭推荐
     * @param userId 用户ID
     * @param count 推荐数量
     * @return 推荐搭配列表
     */
    List<OutfitRecommendationResponse> getOutfitRecommendationsByWuxing(String userId, Integer count);
    
    /**
     * 保存推荐搭配
     * @param userId 用户ID
     * @param recommendationId 推荐ID
     * @return 保存结果
     */
    boolean saveRecommendation(String userId, String recommendationId);
} 