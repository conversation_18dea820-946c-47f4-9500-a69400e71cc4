package com.stylishlink.recommendation;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

/**
 * 推荐服务应用启动类
 */
@SpringBootApplication(scanBasePackages = {"com.stylishlink.recommendation", "com.stylishlink.common"})
@EnableDiscoveryClient
@EnableFeignClients
@EnableMongoRepositories(basePackages = {"com.stylishlink.recommendation.repository"})
public class RecommendationServiceApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(RecommendationServiceApplication.class, args);
    }
} 