package com.stylishlink.recommendation.service.impl;

import com.stylishlink.recommendation.dto.request.OutfitRecommendationRequest;
import com.stylishlink.recommendation.dto.request.OutfitRecommendationRequest.WeatherInfo;
import com.stylishlink.recommendation.dto.request.OutfitRecommendationRequest.WuxingProfile;
import com.stylishlink.recommendation.dto.response.OutfitRecommendationResponse;
import com.stylishlink.recommendation.service.ClothingService;
import com.stylishlink.recommendation.service.RecommendationService;
import com.stylishlink.recommendation.service.UserService;
import com.stylishlink.recommendation.service.WeatherService;
import com.stylishlink.recommendation.service.WuxingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 推荐服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RecommendationServiceImpl implements RecommendationService {

    private final ClothingService clothingService;
    private final UserService userService;
    private final WeatherService weatherService;
    private final WuxingService wuxingService;
    
    @Value("${recommendation.cache.ttl:3600}")
    private int cacheTtl;
    
    @Value("${recommendation.default.count:5}")
    private int defaultRecommendationCount;

    @Override
    @Cacheable(value = "outfitRecommendations", key = "#request.userId + '-' + #request.occasion + '-' + #request.count", unless = "#result == null")
    public List<OutfitRecommendationResponse> getOutfitRecommendations(OutfitRecommendationRequest request) {
        log.debug("获取穿搭推荐，用户ID: {}, 场合: {}, 数量: {}", request.getUserId(), request.getOccasion(), request.getCount());
        
        // 获取用户衣物
        List<Map<String, Object>> userClothing = clothingService.getUserClothing(request.getUserId());
        if (userClothing == null || userClothing.isEmpty()) {
            log.warn("用户衣橱为空，无法生成推荐，用户ID: {}", request.getUserId());
            return Collections.emptyList();
        }
        
        // 获取用户偏好
        Map<String, Object> userPreferences = userService.getUserPreferences(request.getUserId());
        
        // 根据场合筛选衣物
        List<Map<String, Object>> occasionClothing = userClothing;
        if (request.getOccasion() != null && !request.getOccasion().isEmpty()) {
            occasionClothing = clothingService.getUserClothingByOccasion(request.getUserId(), request.getOccasion());
        }
        
        // 根据天气筛选衣物
        List<Map<String, Object>> weatherSuitableClothing = occasionClothing;
        if (request.getWeatherInfo() != null) {
            String[] suitableTypes = weatherService.getSuitableClothingTypes(request.getWeatherInfo());
            weatherSuitableClothing = filterClothingByTypes(occasionClothing, suitableTypes);
        }
        
        // 根据五行命理筛选衣物
        List<Map<String, Object>> wuxingSuitableClothing = weatherSuitableClothing;
        if (request.getWuxingProfile() != null) {
            wuxingSuitableClothing = rankClothingByWuxing(weatherSuitableClothing, request.getWuxingProfile());
        }
        
        // 生成推荐搭配
        int count = request.getCount() != null ? request.getCount() : defaultRecommendationCount;
        List<OutfitRecommendationResponse> recommendations = generateOutfits(
            wuxingSuitableClothing, 
            request.getUserId(), 
            request.getOccasion(), 
            request.getWuxingProfile(),
            count,
            request.getUseAI() != null && request.getUseAI()
        );
        
        log.debug("生成了 {} 个穿搭推荐", recommendations.size());
        return recommendations;
    }

    @Override
    public List<OutfitRecommendationResponse> getOutfitRecommendationsByOccasion(String userId, String occasion, Integer count) {
        OutfitRecommendationRequest request = OutfitRecommendationRequest.builder()
            .userId(userId)
            .occasion(occasion)
            .count(count)
            .build();
        
        return getOutfitRecommendations(request);
    }

    @Override
    public List<OutfitRecommendationResponse> getOutfitRecommendationsByWeather(String userId, String city, Integer count) {
        // 获取当前天气
        WeatherInfo weatherInfo = weatherService.getCurrentWeather(city);
        
        // 获取用户五行命理档案
        WuxingProfile wuxingProfile = wuxingService.getUserWuxingProfile(userId);
        
        OutfitRecommendationRequest request = OutfitRecommendationRequest.builder()
            .userId(userId)
            .weatherInfo(weatherInfo)
            .wuxingProfile(wuxingProfile)
            .count(count)
            .build();
        
        return getOutfitRecommendations(request);
    }

    @Override
    public List<OutfitRecommendationResponse> getOutfitRecommendationsByWuxing(String userId, Integer count) {
        // 获取用户五行命理档案
        WuxingProfile wuxingProfile = wuxingService.getUserWuxingProfile(userId);
        
        OutfitRecommendationRequest request = OutfitRecommendationRequest.builder()
            .userId(userId)
            .wuxingProfile(wuxingProfile)
            .count(count)
            .build();
        
        return getOutfitRecommendations(request);
    }

    @Override
    public boolean saveRecommendation(String userId, String recommendationId) {
        // 此处应实现保存推荐的逻辑，可能需要调用衣橱服务的API
        log.debug("保存推荐，用户ID: {}, 推荐ID: {}", userId, recommendationId);
        return true;
    }
    
    /**
     * 根据衣物类型筛选衣物
     */
    private List<Map<String, Object>> filterClothingByTypes(List<Map<String, Object>> clothing, String[] types) {
        if (types == null || types.length == 0) {
            return clothing;
        }
        
        Set<String> typeSet = new HashSet<>(Arrays.asList(types));
        return clothing.stream()
            .filter(item -> {
                String category = (String) item.get("category");
                return category != null && typeSet.contains(category);
            })
            .collect(Collectors.toList());
    }
    
    /**
     * 根据五行命理对衣物进行排序
     */
    private List<Map<String, Object>> rankClothingByWuxing(List<Map<String, Object>> clothing, WuxingProfile wuxingProfile) {
        if (wuxingProfile == null) {
            return clothing;
        }
        
        // 计算每件衣物的五行匹配度
        List<Map.Entry<Map<String, Object>, Double>> scoredClothing = clothing.stream()
            .map(item -> new AbstractMap.SimpleEntry<>(
                item, 
                wuxingService.calculateMatchScore(item, wuxingProfile)
            ))
            .sorted(Map.Entry.<Map<String, Object>, Double>comparingByValue().reversed())
            .collect(Collectors.toList());
        
        // 返回排序后的衣物
        return scoredClothing.stream()
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
    }
    
    /**
     * 生成穿搭推荐
     */
    private List<OutfitRecommendationResponse> generateOutfits(
        List<Map<String, Object>> clothing, 
        String userId, 
        String occasion, 
        WuxingProfile wuxingProfile,
        int count,
        boolean useAI
    ) {
        List<OutfitRecommendationResponse> recommendations = new ArrayList<>();
        
        // 按类别分组衣物
        Map<String, List<Map<String, Object>>> clothingByCategory = clothing.stream()
            .collect(Collectors.groupingBy(item -> (String) item.get("category")));
        
        // 基本类别
        String[] essentialCategories = {"上衣", "裤子", "鞋子"};
        
        // 生成指定数量的搭配
        for (int i = 0; i < count; i++) {
            try {
                // 为每个基本类别随机选择一件衣物
                List<OutfitRecommendationResponse.OutfitItem> outfitItems = new ArrayList<>();
                
                for (String category : essentialCategories) {
                    List<Map<String, Object>> categoryItems = clothingByCategory.get(category);
                    if (categoryItems != null && !categoryItems.isEmpty()) {
                        Map<String, Object> selectedItem = categoryItems.get(new Random().nextInt(categoryItems.size()));
                        outfitItems.add(mapToOutfitItem(selectedItem));
                    }
                }
                
                // 如果没有足够的基本类别衣物，跳过此搭配
                if (outfitItems.size() < essentialCategories.length) {
                    continue;
                }
                
                // 创建搭配推荐
                OutfitRecommendationResponse recommendation = OutfitRecommendationResponse.builder()
                    .id(UUID.randomUUID().toString())
                    .userId(userId)
                    .name("推荐搭配 #" + (i + 1))
                    .description(generateOutfitDescription(outfitItems, occasion))
                    .items(outfitItems)
                    .occasion(occasion)
                    .seasons(determineSeasons(outfitItems))
                    .styles(determineStyles(outfitItems))
                    .wuxingScores(calculateWuxingScores(outfitItems, wuxingProfile))
                    .totalScore(calculateTotalScore(outfitItems, wuxingProfile))
                    .mainImageUrl("/images/outfits/placeholder.jpg") // 占位图，实际应生成或合成图片
                    .imageUrls(Collections.singletonList("/images/outfits/placeholder.jpg"))
                    .isAiGenerated(useAI)
                    .reasoningExplanation(generateReasoning(outfitItems, occasion, wuxingProfile))
                    .createdAt(LocalDateTime.now())
                    .build();
                
                recommendations.add(recommendation);
            } catch (Exception e) {
                log.error("生成搭配时发生错误", e);
            }
        }
        
        return recommendations;
    }
    
    /**
     * 将衣物数据映射为搭配项
     */
    private OutfitRecommendationResponse.OutfitItem mapToOutfitItem(Map<String, Object> clothingItem) {
        return OutfitRecommendationResponse.OutfitItem.builder()
            .type("clothing")
            .itemId((String) clothingItem.get("id"))
            .category((String) clothingItem.get("category"))
            .name((String) clothingItem.get("name"))
            .imageUrl((String) clothingItem.get("imageUrl"))
            .color((String) clothingItem.get("color"))
            .material((String) clothingItem.get("material"))
            .pattern((String) clothingItem.get("pattern"))
            .wuxingAttributes((Map<String, Double>) clothingItem.get("wuxingAttributes"))
            .build();
    }
    
    /**
     * 生成搭配描述
     */
    private String generateOutfitDescription(List<OutfitRecommendationResponse.OutfitItem> items, String occasion) {
        StringBuilder description = new StringBuilder();
        
        if (occasion != null && !occasion.isEmpty()) {
            description.append("适合").append(occasion).append("场合的");
        }
        
        description.append("搭配，包含");
        
        for (int i = 0; i < items.size(); i++) {
            OutfitRecommendationResponse.OutfitItem item = items.get(i);
            
            if (i > 0) {
                if (i == items.size() - 1) {
                    description.append("和");
                } else {
                    description.append("、");
                }
            }
            
            description.append(item.getColor()).append(item.getName());
        }
        
        return description.toString();
    }
    
    /**
     * 确定搭配适合的季节
     */
    private List<String> determineSeasons(List<OutfitRecommendationResponse.OutfitItem> items) {
        // 简化实现，实际应根据衣物属性综合判断
        return Arrays.asList("春季", "秋季");
    }
    
    /**
     * 确定搭配的风格
     */
    private List<String> determineStyles(List<OutfitRecommendationResponse.OutfitItem> items) {
        // 简化实现，实际应根据衣物属性综合判断
        return Arrays.asList("休闲", "日常");
    }
    
    /**
     * 计算搭配的五行得分
     */
    private Map<String, Double> calculateWuxingScores(List<OutfitRecommendationResponse.OutfitItem> items, WuxingProfile wuxingProfile) {
        if (wuxingProfile == null) {
            return Map.of("金", 0.0, "木", 0.0, "水", 0.0, "火", 0.0, "土", 0.0);
        }
        
        // 简化实现，实际应根据衣物五行属性和用户五行命理综合计算
        Map<String, Double> scores = new HashMap<>();
        scores.put("金", 70.0);
        scores.put("木", 60.0);
        scores.put("水", 80.0);
        scores.put("火", 50.0);
        scores.put("土", 65.0);
        
        return scores;
    }
    
    /**
     * 计算搭配的总得分
     */
    private Double calculateTotalScore(List<OutfitRecommendationResponse.OutfitItem> items, WuxingProfile wuxingProfile) {
        if (wuxingProfile == null) {
            return 70.0;
        }
        
        // 简化实现，实际应根据衣物五行属性和用户五行命理综合计算
        return 85.5;
    }
    
    /**
     * 生成推荐理由
     */
    private String generateReasoning(List<OutfitRecommendationResponse.OutfitItem> items, String occasion, WuxingProfile wuxingProfile) {
        StringBuilder reasoning = new StringBuilder();
        
        reasoning.append("此搭配");
        
        if (occasion != null && !occasion.isEmpty()) {
            reasoning.append("适合").append(occasion).append("场合，");
        }
        
        reasoning.append("色彩搭配和谐，风格统一。");
        
        if (wuxingProfile != null) {
            reasoning.append("根据您的五行命理分析，此搭配的五行属性与您相匹配，有助于增强您的气场和运势。");
        }
        
        return reasoning.toString();
    }
} 