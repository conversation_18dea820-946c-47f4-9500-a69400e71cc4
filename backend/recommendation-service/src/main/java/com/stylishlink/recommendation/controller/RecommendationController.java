package com.stylishlink.recommendation.controller;

import com.stylishlink.recommendation.dto.request.OutfitRecommendationRequest;
import com.stylishlink.recommendation.dto.response.ApiResponse;
import com.stylishlink.recommendation.dto.response.OutfitRecommendationResponse;
import com.stylishlink.recommendation.service.RecommendationService;
import com.stylishlink.recommendation.service.UserService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 推荐控制器
 */
@RestController
@RequestMapping("/recommendation")
@RequiredArgsConstructor
@Slf4j
public class RecommendationController {

    private final RecommendationService recommendationService;
    private final UserService userService;

    /**
     * 获取穿搭推荐
     * @param request 推荐请求
     * @return 推荐列表
     */
    @PostMapping("/outfit")
    public ResponseEntity<ApiResponse<List<OutfitRecommendationResponse>>> getOutfitRecommendations(
        @Valid @RequestBody OutfitRecommendationRequest request
    ) {
        log.info("获取穿搭推荐，用户ID: {}, 场合: {}", request.getUserId(), request.getOccasion());
        
        List<OutfitRecommendationResponse> recommendations = recommendationService.getOutfitRecommendations(request);
        
        return ResponseEntity.ok(ApiResponse.success("获取穿搭推荐成功", recommendations));
    }
    
    /**
     * 根据场合获取穿搭推荐
     * @param userId 用户ID
     * @param occasion 场合
     * @param count 数量
     * @return 推荐列表
     */
    @GetMapping("/outfit/occasion")
    public ResponseEntity<ApiResponse<List<OutfitRecommendationResponse>>> getOutfitRecommendationsByOccasion(
        @RequestParam("userId") String userId,
        @RequestParam("occasion") String occasion,
        @RequestParam(value = "count", required = false) Integer count
    ) {
        log.info("根据场合获取穿搭推荐，用户ID: {}, 场合: {}, 数量: {}", userId, occasion, count);
        
        List<OutfitRecommendationResponse> recommendations = 
            recommendationService.getOutfitRecommendationsByOccasion(userId, occasion, count);
        
        return ResponseEntity.ok(ApiResponse.success("根据场合获取穿搭推荐成功", recommendations));
    }
    
    /**
     * 根据天气获取穿搭推荐
     * @param userId 用户ID
     * @param city 城市
     * @param count 数量
     * @return 推荐列表
     */
    @GetMapping("/outfit/weather")
    public ResponseEntity<ApiResponse<List<OutfitRecommendationResponse>>> getOutfitRecommendationsByWeather(
        @RequestParam("userId") String userId,
        @RequestParam("city") String city,
        @RequestParam(value = "count", required = false) Integer count
    ) {
        log.info("根据天气获取穿搭推荐，用户ID: {}, 城市: {}, 数量: {}", userId, city, count);
        
        List<OutfitRecommendationResponse> recommendations = 
            recommendationService.getOutfitRecommendationsByWeather(userId, city, count);
        
        return ResponseEntity.ok(ApiResponse.success("根据天气获取穿搭推荐成功", recommendations));
    }
    
    /**
     * 根据五行命理获取穿搭推荐
     * @param userId 用户ID
     * @param count 数量
     * @return 推荐列表
     */
    @GetMapping("/outfit/wuxing")
    public ResponseEntity<ApiResponse<List<OutfitRecommendationResponse>>> getOutfitRecommendationsByWuxing(
        @RequestParam("userId") String userId,
        @RequestParam(value = "count", required = false) Integer count
    ) {
        log.info("根据五行命理获取穿搭推荐，用户ID: {}, 数量: {}", userId, count);
        
        List<OutfitRecommendationResponse> recommendations = 
            recommendationService.getOutfitRecommendationsByWuxing(userId, count);
        
        return ResponseEntity.ok(ApiResponse.success("根据五行命理获取穿搭推荐成功", recommendations));
    }
    
    /**
     * 保存推荐
     * @param userId 用户ID
     * @param recommendationId 推荐ID
     * @return 保存结果
     */
    @PostMapping("/save")
    public ResponseEntity<ApiResponse<Boolean>> saveRecommendation(
        @RequestParam("userId") String userId,
        @RequestParam("recommendationId") String recommendationId
    ) {
        log.info("保存推荐，用户ID: {}, 推荐ID: {}", userId, recommendationId);
        
        boolean result = recommendationService.saveRecommendation(userId, recommendationId);
        
        return ResponseEntity.ok(ApiResponse.success("保存推荐" + (result ? "成功" : "失败"), result));
    }
} 