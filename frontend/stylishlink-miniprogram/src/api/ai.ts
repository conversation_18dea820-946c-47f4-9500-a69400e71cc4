import { get, post } from '@/utils/request'

/**
 * 体型识别结果接口
 */
export interface BodyShapeAnalysisResult {
  fullBodyPhoto: boolean // 是否为正面全身照
  confidence: number // 识别置信度 0-100
  bodyType: string // 体型分类（如"沙漏形"、"梨形"等）
  bodyShape: {
    shoulderWidth: number // 肩膀宽度 1-5
    waistShape: number // 腰型 1-5
    belly: number // 肚腩 1-5
    hip: number // 臀型 1-5
    hipShape: number // 胯型 1-5
    armLength: number // 臂长 1-5
    armCircum: number // 臂围 1-5
    hipWidth: number // 胯部宽度 1-5
    thigh: number // 大腿 1-5
    calf: number // 小腿 1-5
    bodyFat: number // 上下身比例 1-5
    bodyLength: number // 上下身长度比例 1-5
  }
  analysis: Array<{
    feature: string // 分析特征点
    description: string // 特征描述
    type: string // 类型（positive/neutral）
  }>
  suggestions: Array<{
    category: string // 建议类别
    content: string // 建议内容
    priority: string // 优先级
  }>
  reason: string | null // 识别失败原因
  timestamp: number // 时间戳
}

/**
 * 体型识别API（通过文件ID）
 * @param fileId 文件ID
 * @returns Promise<BodyShapeAnalysisResult>
 */
export async function analyzeBodyShape(fileId: string): Promise<BodyShapeAnalysisResult> {
  const response = await post<BodyShapeAnalysisResult>(
    '/api/user/analyze-body-shape-by-file-id',
    {
      fileId,
      analysisType: 'full', // 完整分析模式
    },
  )
  return response.data.data
}

/**
 * 体型识别API（直接上传文件）
 * @param file 图片文件
 * @returns Promise<BodyShapeAnalysisResult>
 */
export async function analyzeBodyShapeByFile(file: File): Promise<BodyShapeAnalysisResult> {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('analysisType', 'full')

  const response = await post<BodyShapeAnalysisResult>(
    '/api/user/analyze-body-shape',
    formData,
    {
      header: {
        'Content-Type': 'multipart/form-data',
      },
    },
  )
  return response.data.data
}

/**
 * 服装识别API
 * @param imageUrl 图片URL
 * @returns Promise<ClothingAnalysisResult>
 */
export interface ClothingAnalysisResult {
  category: string // 服装类别
  color: string[] // 主要颜色
  style: string // 风格
  season: string[] // 适合季节
  occasion: string[] // 适合场合
  confidence: number // 识别置信度
}

export async function analyzeClothing(imageUrl: string): Promise<ClothingAnalysisResult> {
  const response = await post<ClothingAnalysisResult>(
    '/api/ai/analyze/clothing',
    {
      imageUrl,
      analysisType: 'detail',
    },
  )
  return response.data.data
}

/**
 * 穿搭推荐API
 * @param userProfile 用户信息
 * @param weather 天气信息
 * @param occasion 场合
 * @returns Promise<OutfitRecommendation[]>
 */
export interface OutfitRecommendation {
  id: string
  name: string
  items: {
    category: string
    imageUrl: string
    color: string
    brand?: string
  }[]
  score: number // 推荐分数
  reason: string[] // 推荐理由
  tags: string[] // 标签
}

export interface RecommendationRequest {
  userProfile: {
    bodyType: string
    skinTone: string
    stylePreferences: string[]
    height: number
    weight: number
  }
  weather?: {
    temperature: number
    condition: string
    humidity: number
  }
  occasion?: string
  limit?: number
}

export async function getOutfitRecommendations(params: RecommendationRequest): Promise<OutfitRecommendation[]> {
  const response = await post<OutfitRecommendation[]>(
    '/api/ai/recommend/outfit',
    params,
  )
  return response.data.data
}

/**
 * 虚拟试衣响应接口
 */
export interface VirtualTryOnResponse {
  resultImageUrl: string
}

/**
 * 虚拟试衣API
 * @param userImageUrl 用户照片URL
 * @param clothingImageUrl 服装图片URL
 * @returns Promise<string> 试衣结果图片URL
 */
export async function virtualTryOn(userImageUrl: string, clothingImageUrl: string): Promise<string> {
  const response = await post<VirtualTryOnResponse>(
    '/api/ai/virtual-try-on',
    {
      userImageUrl,
      clothingImageUrl,
      quality: 'high',
    },
  )
  return response.data.data.resultImageUrl
}

/**
 * 天气API请求参数
 */
export interface WeatherRequest {
  city: string // 城市名称，如"北京"、"上海"等
}

/**
 * 天气API响应数据
 */
export interface WeatherResponse {
  city: string // 城市名称
  temperature: number // 温度（摄氏度）
  condition: string // 天气状况文字描述，如"晴"、"多云"、"雨"
  icon: string // 天气图标类名
  humidity?: number // 湿度百分比 (可选)
  windSpeed?: number // 风速 (可选)
  windDirection?: string // 风向 (可选)
  airQuality?: string // 空气质量 (可选)
  updateTime?: string // 更新时间 (可选)
}

/**
 * 获取天气信息
 * @param city 城市名称
 * @returns 天气数据
 */
export async function getWeather(city: string): Promise<WeatherResponse> {
  try {
    const response = await get<any>(
      `/api/user/weather?city=${encodeURIComponent(city)}`,
    )

    if (response.data.code === 200 || response.data.code === '200') {
      const rawData = response.data.data

      // 解析嵌套的数据结构，转换为前端期望的格式
      const weatherData: WeatherResponse = {
        city: rawData.location?.name || city, // 从location.name获取城市名，回退到请求参数
        temperature: Number.parseFloat(rawData.current?.temperature) || 0, // 温度字符串转数字
        condition: rawData.current?.text || '未知', // 从current.text获取天气状况
        icon: getWeatherIcon(rawData.current?.text || ''), // 根据天气状况生成图标
        humidity: rawData.current?.humidity ? Number.parseFloat(rawData.current.humidity) : undefined,
        windSpeed: rawData.current?.windSpeed ? Number.parseFloat(rawData.current.windSpeed) : undefined,
        windDirection: rawData.current?.windDirection || undefined,
        airQuality: rawData.current?.airQuality || undefined,
        updateTime: rawData.lastUpdate || undefined,
      }

      return weatherData
    }
    else {
      throw new Error(response.data.message || response.data.msg || '获取天气信息失败')
    }
  }
  catch (error) {
    console.error('获取天气信息失败:', error)
    throw error
  }
}

/**
 * 天气图标映射
 * 将后端返回的天气状况映射为对应的iconfont图标类名
 */
export const WEATHER_ICON_MAP: Record<string, string> = {
  晴: 'icon-taiyang',
  多云: 'icon-duoyun',
  阴: 'icon-yin',
  小雨: 'icon-xiaoyu',
  中雨: 'icon-zhongyu',
  大雨: 'icon-dayu',
  暴雨: 'icon-baoyu',
  雷阵雨: 'icon-leizhenyu',
  雪: 'icon-xue',
  小雪: 'icon-xiaoxue',
  中雪: 'icon-zhongxue',
  大雪: 'icon-daxue',
  雾: 'icon-wu',
  霾: 'icon-mai',
  沙尘暴: 'icon-shachenbao',
}

/**
 * 获取天气图标
 * @param condition 天气状况
 * @returns iconfont图标类名
 */
export function getWeatherIcon(condition: string): string {
  return WEATHER_ICON_MAP[condition] || 'icon-duoyun' // 默认返回多云图标，避免误导
}
