<script setup lang="ts">
import type { WuxingAnalysis } from '@/api/fortune'
import { computed } from 'vue'
import WuxingCircle from '@/components/business/WuxingCircle.vue'

interface Props {
  wuxingData: WuxingAnalysis
}

const props = defineProps<Props>()

// 将API数据转换为WuxingCircle组件需要的格式
const wuxingCircleData = computed(() => {
  return props.wuxingData.elements.map(item => ({
    element: item.element as '金' | '木' | '水' | '火' | '土',
    percentage: item.percentage,
    isHighlight: item.isRizhu, // 日主高亮显示
  }))
})
</script>

<template>
  <view class="wuxing-analysis-card">
    <view class="card-header">
      <text class="card-title">
        五行分析
      </text>
    </view>

    <!-- 使用WuxingCircle组件 -->
    <view class="wuxing-diagram-container">
      <WuxingCircle
        :wuxing-data="wuxingCircleData"
        size="large"
        :show-relations="true"
        :show-labels="true"
      />
    </view>

    <!-- 五行分析描述 -->
    <view class="advice-text">
      <text class="advice-content">
        {{ props.wuxingData.analysis }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.wuxing-analysis-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 16px;
}

.card-header {
  margin-bottom: 20px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary, #333333);
}

.wuxing-diagram-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20px 0 32px 0;
  min-height: 300px; /* 🔑 关键：确保足够高度显示large尺寸的Canvas组件，与测试页面保持一致 */
  padding: 16px 0;

  // 🔑 确保Canvas容器有正确的层级和渲染上下文
  position: relative;
  z-index: 1;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

.advice-text {
  margin-top: 16px;
  text-align: center;
}

.advice-content {
  font-size: 12px;
  color: var(--color-text-secondary, #666666);
  line-height: 1.5;
}

// 响应式调整
@media (max-width: 375px) {
  .wuxing-analysis-card {
    padding: 16px;
  }

  .wuxing-diagram-container {
    margin: 16px 0 24px 0;
  }
}
</style>
