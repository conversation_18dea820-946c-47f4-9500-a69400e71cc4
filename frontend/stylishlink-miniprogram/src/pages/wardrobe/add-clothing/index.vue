<script setup lang="ts">
import { computed, ref } from 'vue'
import GlassCard from '@/components/common/GlassCard.vue'
import { useMenuButton } from '@/composables/useMenuButton'
// import { useWardrobeApi } from '@/api/wardrobe'
import { mockAIClothingRecognition } from '@/utils/mock-ai'
import { getWuxingColor, getWuxingIcon } from '@/utils/wuxing'

// 状态管理
const uploadedImage = ref<string>('')
const isAIProcessing = ref(false)
const aiResult = ref<any>(null)
const clothingName = ref('')
const notes = ref('')

// 胶囊按钮适配
const { contentTop, contentOffset12px } = useMenuButton()

// 季节选择
const selectedSeasons = ref<string[]>(['春', '夏', '秋', '冬'])
const seasons = [
  { key: '春', label: '春季', icon: 'icon-chuntian', color: 'linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, rgba(102, 187, 106, 0.2) 100%)' },
  { key: '夏', label: '夏季', icon: 'icon-xiatian-1', color: 'linear-gradient(135deg, rgba(255, 152, 0, 0.2) 0%, rgba(255, 193, 7, 0.2) 100%)' },
  { key: '秋', label: '秋季', icon: 'icon-fengye1', color: 'linear-gradient(135deg, rgba(255, 87, 34, 0.2) 0%, rgba(244, 67, 54, 0.2) 100%)' },
  { key: '冬', label: '冬季', icon: 'icon-xue', color: 'linear-gradient(135deg, rgba(33, 150, 243, 0.2) 0%, rgba(63, 81, 181, 0.2) 100%)' },
]

// 场合选择
const selectedOccasions = ref<string[]>(['日常休闲', '职场商务'])
const occasions = [
  { key: '日常休闲', label: '日常休闲' },
  { key: '职场商务', label: '职场商务' },
  { key: '约会交际', label: '约会交际' },
  { key: '派对活动', label: '派对活动' },
  { key: '运动健身', label: '运动健身' },
  { key: '正式场合', label: '正式场合' },
]

// API
// const wardrobeApi = useWardrobeApi()

// 计算属性
const hasUploadedImage = computed(() => !!uploadedImage.value)
const canSave = computed(() => hasUploadedImage.value && !!aiResult.value)

// 页面初始化 - 移除不必要的异步初始化

// 图片上传
async function chooseImage() {
  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera', 'album'],
    })

    if (res.tempFilePaths.length > 0) {
      uploadedImage.value = res.tempFilePaths[0]

      // 开始AI识别
      isAIProcessing.value = true
      try {
        // 模拟AI识别
        const result = await mockAIClothingRecognition(uploadedImage.value)
        aiResult.value = result
        clothingName.value = result.name || ''
      }
      catch (error) {
        console.error('AI识别失败:', error)
        uni.showToast({
          title: 'AI识别失败',
          icon: 'none',
        })
      }
      finally {
        isAIProcessing.value = false
      }
    }
  }
  catch (error) {
    console.error('选择图片失败:', error)
    uni.showToast({
      title: '选择图片失败',
      icon: 'none',
    })
  }
}

// 季节选择切换
function toggleSeason(season: string) {
  const index = selectedSeasons.value.indexOf(season)
  if (index > -1) {
    selectedSeasons.value.splice(index, 1)
  }
  else {
    selectedSeasons.value.push(season)
  }
}

// 场合选择切换
function toggleOccasion(occasion: string) {
  const index = selectedOccasions.value.indexOf(occasion)
  if (index > -1) {
    selectedOccasions.value.splice(index, 1)
  }
  else {
    selectedOccasions.value.push(occasion)
  }
}

// 保存衣物
async function saveClothing() {
  if (!canSave.value) {
    if (!hasUploadedImage.value) {
      uni.showToast({
        title: '请先上传图片',
        icon: 'none',
      })
    }
    else if (!aiResult.value) {
      uni.showToast({
        title: '请等待AI识别完成',
        icon: 'none',
      })
    }
    return
  }

  try {
    uni.showLoading({ title: '保存中...' })

    const clothingData = {
      name: clothingName.value,
      category: aiResult.value?.category || '上衣',
      subCategory: aiResult.value?.subCategory || '',
      colors: aiResult.value?.colors || [],
      seasons: selectedSeasons.value,
      occasions: selectedOccasions.value,
      materials: aiResult.value?.materials || [],
      wuxing: aiResult.value?.wuxing || {},
      mainImageUrl: uploadedImage.value,
      description: notes.value,
    }

    // 临时模拟保存成功
    console.warn('保存的衣物数据:', clothingData)

    uni.hideLoading()
    uni.showToast({
      title: '保存成功',
      icon: 'success',
    })

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
  catch (error) {
    uni.hideLoading()
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
  }
}

// 返回上一页
function handleBack() {
  uni.navigateBack()
}
</script>

<template>
  <view class="page-container">
    <!-- 渐变背景 -->
    <view class="gradient-background" />

    <!-- 页面标题 - 参照衣橱页面的简洁实现 -->
    <view class="page-header" :style="{ top: contentTop }">
      <view class="header-left" @click="handleBack">
        <text class="iconfont icon-fanhuiicon back-icon" />
      </view>
      <text class="page-title">
        添加衣物
      </text>
    </view>

    <!-- 滚动容器 - 参照衣橱页面布局 -->
    <scroll-view
      class="main-scroll-container"
      enable-flex
      scroll-y
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="false"
      :style="{ paddingTop: `calc(${contentOffset12px} + 10px)` }"
    >
      <view class="main-content">
        <!-- 上传图片区域 -->
        <view class="upload-section">
          <GlassCard>
            <view class="upload-container" @click="chooseImage">
              <!-- 有图片时显示 -->
              <view v-if="hasUploadedImage" class="preview-container">
                <image
                  :src="uploadedImage"
                  class="preview-image"
                  mode="aspectFit"
                />
                <!-- AI扫描动画 -->
                <view v-if="isAIProcessing" class="ai-scan-overlay">
                  <view class="ai-scan-line" />
                  <text class="ai-scan-text">
                    AI识别中...
                  </text>
                </view>
              </view>

              <!-- 无图片时显示 -->
              <view v-else class="upload-placeholder">
                <view class="upload-icon">
                  <text class="iconfont icon-xiangji" />
                </view>
                <text class="upload-title">
                  添加衣物照片
                </text>
                <text class="upload-subtitle">
                  拍摄清晰的衣物照片
                </text>
              </view>
            </view>
          </GlassCard>
        </view>

        <!-- AI识别结果 -->
        <view v-if="aiResult" class="ai-result-section">
          <GlassCard>
            <view class="ai-result-header">
              <text class="ai-result-title">
                AI识别结果
              </text>
              <view class="ai-status">
                <view v-if="isAIProcessing" class="loading-indicator">
                  <view class="loading-spinner" />
                  <text class="loading-text">
                    识别中...
                  </text>
                </view>
              </view>
            </view>

            <view class="ai-result-content">
              <view class="result-item">
                <text class="result-label">
                  服装种类
                </text>
                <text class="result-value">
                  {{ aiResult.category || '未识别' }}
                </text>
              </view>
              <view class="result-item">
                <text class="result-label">
                  主要颜色
                </text>
                <text class="result-value">
                  {{ aiResult.colors?.join('、') || '未识别' }}
                </text>
              </view>
              <view class="result-item">
                <text class="result-label">
                  材质推测
                </text>
                <text class="result-value">
                  {{ aiResult.materials?.join('、') || '未识别' }}
                </text>
              </view>
              <view class="result-item">
                <text class="result-label">
                  五行属性
                </text>
                <view v-if="aiResult.wuxing?.primary" class="wuxing-display">
                  <text
                    class="iconfont wuxing-icon"
                    :class="getWuxingIcon(aiResult.wuxing.primary)"
                    :style="{ color: getWuxingColor(aiResult.wuxing.primary) }"
                  />
                  <text class="wuxing-text">
                    {{ aiResult.wuxing.primary }}行
                  </text>
                </view>
                <text v-else class="result-value">
                  未识别
                </text>
              </view>
            </view>

            <view class="ai-result-footer">
              <text class="ai-note">
                AI已完成基础识别，您可以修改或补充以下信息
              </text>
            </view>
          </GlassCard>
        </view>

        <!-- 季节适应性 -->
        <view v-if="aiResult" class="season-section">
          <GlassCard>
            <view class="section-header">
              <text class="section-title">
                季节适应性
              </text>
            </view>

            <view class="season-options">
              <view
                v-for="season in seasons"
                :key="season.key"
                class="season-item"
                :class="{ active: selectedSeasons.includes(season.key) }"
                :style="{ background: selectedSeasons.includes(season.key) ? season.color : 'rgba(255, 255, 255, 0.2)' }"
                @click="toggleSeason(season.key)"
              >
                <text class="iconfont season-icon" :class="season.icon" />
                <text class="season-label">
                  {{ season.label }}
                </text>
              </view>
            </view>
          </GlassCard>
        </view>

        <!-- 场合适用性 -->
        <view v-if="aiResult" class="occasion-section">
          <GlassCard>
            <view class="section-header">
              <text class="section-title">
                场合适用性
              </text>
            </view>

            <view class="occasion-options">
              <view
                v-for="occasion in occasions"
                :key="occasion.key"
                class="occasion-item"
                :class="{ active: selectedOccasions.includes(occasion.key) }"
                @click="toggleOccasion(occasion.key)"
              >
                <text class="occasion-label">
                  {{ occasion.label }}
                </text>
              </view>
            </view>
          </GlassCard>
        </view>

        <!-- 备注信息 -->
        <view v-if="aiResult" class="notes-section">
          <GlassCard>
            <view class="section-header">
              <text class="section-title">
                备注信息
              </text>
            </view>

            <view class="form-item">
              <textarea
                v-model="notes"
                class="form-textarea"
                placeholder="添加一些备注信息，如购买地点、搭配建议等"
                placeholder-class="form-placeholder"
                :maxlength="200"
                :auto-height="false"
                rows="2"
              />
            </view>
          </GlassCard>
        </view>

        <!-- 保存按钮 -->
        <view class="save-section">
          <view
            class="save-button"
            :class="{ disabled: !canSave }"
            @click="saveClothing"
          >
            <text class="save-text">
              保存到衣橱
            </text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  position: relative;
}

.gradient-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #c2e9fb 0%, #d8c6fd 100%);
  z-index: -1;
}

.page-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  height: 28px;
  display: flex;
  align-items: center;
  padding: 0 5%;
}

.header-left {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

.back-icon {
  font-size: 18px;
  font-weight: bold;
  color: #1e293b;
}

.page-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.main-content {
  padding: 0 5%;
  padding-bottom: 40px;
}

// 上传区域
.upload-section {
  margin-bottom: 16px;
}

.upload-container {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: 16px;
  cursor: pointer;
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.ai-scan-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(168, 230, 207, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.ai-scan-line {
  width: 80%;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(168, 230, 207, 0.8) 50%,
    transparent 100%
  );
  animation: scan 2s linear infinite;
}

@keyframes scan {
  0% { transform: translateY(-100px); }
  100% { transform: translateY(100px); }
}

.ai-scan-text {
  font-size: 14px;
  color: #4ade80;
  font-weight: 500;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 12px;
}

.upload-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;

  .iconfont {
    font-size: 28px;
    color: var(--color-text-primary, #333333);
  }
}

.upload-title {
  font-size: 14px;
  color: var(--color-text-primary, #333333);
  font-weight: 500;
}

.upload-subtitle {
  font-size: 12px;
  color: var(--color-text-secondary, #666666);
}

// AI识别结果
.ai-result-section {
  margin-bottom: 16px;
}

.ai-result-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 16px;
}

.ai-result-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary, #333333);
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #4ade80;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 12px;
  color: var(--color-text-secondary, #666666);
}

.ai-result-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-label {
  font-size: 12px;
  color: var(--color-text-secondary, #666666);
}

.result-value {
  font-size: 12px;
  color: var(--color-text-primary, #333333);
  text-align: right;

  &.wuxing-tag {
    padding: 2px 8px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.5);
  }
}

.ai-result-footer {
  padding-top: 8px;
}

.ai-note {
  font-size: 10px;
  color: var(--color-text-secondary, #666666);
  line-height: 1.4;
}

// 表单区域
.season-section,
.occasion-section,
.notes-section {
  margin-bottom: 16px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary, #333333);
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 12px;
  color: var(--color-text-secondary, #666666);
  font-weight: 500;
}

.form-input {
  padding: 12px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  font-size: 14px;
  color: var(--color-text-primary, #333333);
}

.form-textarea {
  padding: 12px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  font-size: 14px;
  color: var(--color-text-primary, #333333);
  height: 80px;
  resize: none;
  box-sizing: border-box;
}

.form-placeholder {
  color: var(--color-text-tertiary, #999999);
}

// 季节选择
.season-options {
  display: flex;
  justify-content: between;
  gap: 12px;
}

.season-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 6px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    // 选中状态不需要特殊样式，通过 :style 动态设置背景色
  }
}

.season-icon {
  font-size: 18px;
}

.season-label {
  font-size: 10px;
  color: var(--color-text-secondary, #666666);
}

// 场合选择
.occasion-options {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
}

.occasion-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.8) 0%, rgba(124, 58, 237, 0.8) 100%);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);

    .occasion-label {
      color: #ffffff;
      font-weight: 500;
      text-align: center;
      width: 100%;
    }
  }
}

.occasion-label {
  font-size: 11px;
  color: var(--color-text-secondary, #666666);
  line-height: 1.2;
  text-align: center;
  width: 100%;
}

// 保存按钮
.save-section {
  margin-top: 24px;
  margin-bottom: 20px;
}

.save-button {
  width: calc(100% - 0px);
  padding: 14px 16px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;

  // 默认启用状态 - AI识别完成后
  background: linear-gradient(135deg, rgba(139, 92, 246, 1.0) 0%, rgba(124, 58, 237, 1.0) 100%);
  border: 1px solid rgba(139, 92, 246, 0.6);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);

  // 禁用状态 - AI识别未完成
  &.disabled {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.4) 0%, rgba(124, 58, 237, 0.4) 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.2);
    cursor: not-allowed;
    opacity: 1; // 移除额外的opacity设置，通过背景色透明度控制
  }
}

.save-text {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

// 响应式调整
@media (max-width: 375px) {
  .main-content {
    padding: 0 4%;
  }

  .season-options {
    gap: 8px;
  }

  .season-item {
    padding: 12px 6px;
    gap: 6px;
  }

  .season-icon {
    font-size: 18px;
  }

  .occasion-options {
    gap: 6px;
  }

  .occasion-item {
    padding: 10px;
    gap: 10px;
  }
}

// 五行属性显示
.wuxing-display {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 8px;
}

.wuxing-icon {
  font-size: 14px;
  font-weight: bold;
}

.wuxing-text {
  font-size: 12px;
  font-weight: 600;
  color: var(--color-text-primary, #333333);
}

// 毛玻璃效果 - 去掉边框
.enhanced-glass {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.85) 0%,
    rgba(255, 255, 255, 0.65) 50%,
    rgba(255, 255, 255, 0.85) 100%
  );
  box-shadow:
    0 -8px 32px rgba(0, 0, 0, 0.06),
    0 -2px 8px rgba(0, 0, 0, 0.03);
}
</style>
