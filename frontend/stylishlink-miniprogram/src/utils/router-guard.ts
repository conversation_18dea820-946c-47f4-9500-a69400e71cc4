/**
 * 路由守卫工具
 * 负责全局页面跳转的登录态检查
 */

import { useUserStore } from '@/store/user'
import { safeGetApp } from '@/utils/app'

// 不需要登录检查的页面白名单
const NO_AUTH_PAGES = [
  '/pages/index/index', // 首页
  '/pages/login/index', // 登录页
  '/pages/test/index', // 测试页（开发环境）
  // 未来可能添加的页面
  '/pages/about/index', // 关于页
  '/pages/privacy/index', // 隐私协议页
  '/pages/terms/index', // 用户条款页
  '/pages/help/index', // 帮助页
]

// Tab页面列表（需要特殊处理）
const TAB_PAGES = [
  '/pages/index/index',
  '/pages/wardrobe/index',
  '/pages/outfit/index',
  '/pages/profile/index',
]

/**
 * 安全获取用户Store
 * @returns UserStore实例或null
 */
function safeGetUserStore() {
  try {
    // 检查App实例是否可用
    const app = safeGetApp()
    if (!app) {
      console.warn('App实例不可用，跳过登录检查')
      return null
    }

    return useUserStore()
  }
  catch (error) {
    console.warn('获取UserStore失败:', error)
    return null
  }
}

/**
 * 检查页面是否需要登录
 * @param url 页面路径
 * @returns 是否需要登录检查
 */
export function needsAuth(url: string): boolean {
  // 提取路径部分（去除参数）
  const path = url.split('?')[0]

  // 检查是否在白名单中
  return !NO_AUTH_PAGES.includes(path)
}

/**
 * 检查是否为Tab页面
 * @param url 页面路径
 * @returns 是否为Tab页面
 */
export function isTabPage(url: string): boolean {
  const path = url.split('?')[0]
  return TAB_PAGES.includes(path)
}

/**
 * 路由守卫核心检查函数
 * @param url 目标页面路径
 * @param isTabSwitch 是否为Tab切换
 * @returns Promise<boolean> 是否允许跳转
 */
export async function routeGuard(url: string, isTabSwitch = false): Promise<boolean> {
  // 如果页面不需要登录检查，直接通过
  if (!needsAuth(url)) {
    return true
  }

  const userStore = safeGetUserStore()

  // 如果Store不可用，暂时允许跳转（避免阻塞）
  if (!userStore) {
    console.warn('UserStore不可用，暂时允许跳转:', url)
    return true
  }

  // 检查登录状态
  if (!userStore.isLoggedIn) {
    // Tab切换时使用静默模式（直接跳转），其他情况弹出提示
    if (isTabSwitch) {
      // 静默跳转到登录页
      uni.navigateTo({
        url: '/pages/login/index',
      })
    }
    else {
      // 弹出提示后跳转
      await userStore.checkLoginStatus()
    }

    return false
  }

  return true
}

/**
 * 安装全局路由守卫
 */
export function installRouterGuard() {
  // 拦截 navigateTo
  uni.addInterceptor('navigateTo', {
    invoke(args: any) {
      if (!needsAuth(args.url)) {
        return args // 不需要登录检查，直接通过
      }

      const userStore = safeGetUserStore()
      if (!userStore) {
        console.warn('UserStore不可用，允许跳转:', args.url)
        return args
      }

      if (!userStore.isLoggedIn) {
        // 阻止跳转，静默跳转到登录页
        userStore.checkLoginStatus(true)
        return false
      }

      return args // 已登录，允许跳转
    },
  })

  // 拦截 redirectTo
  uni.addInterceptor('redirectTo', {
    invoke(args: any) {
      if (!needsAuth(args.url)) {
        return args // 不需要登录检查，直接通过
      }

      const userStore = safeGetUserStore()
      if (!userStore) {
        console.warn('UserStore不可用，允许跳转:', args.url)
        return args
      }

      if (!userStore.isLoggedIn) {
        // 阻止跳转，静默跳转到登录页
        userStore.checkLoginStatus(true)
        return false
      }

      return args // 已登录，允许跳转
    },
  })

  // 拦截 switchTab
  uni.addInterceptor('switchTab', {
    invoke(args: any) {
      if (!needsAuth(args.url)) {
        return args // 不需要登录检查，直接通过
      }

      const userStore = safeGetUserStore()
      if (!userStore) {
        console.warn('UserStore不可用，允许跳转:', args.url)
        return args
      }

      if (!userStore.isLoggedIn) {
        // Tab切换时静默跳转到登录页
        userStore.checkLoginStatus(true)
        return false
      }

      return args // 已登录，允许跳转
    },
  })

  // 拦截 reLaunch
  uni.addInterceptor('reLaunch', {
    invoke(args: any) {
      if (!needsAuth(args.url)) {
        return args // 不需要登录检查，直接通过
      }

      const userStore = safeGetUserStore()
      if (!userStore) {
        console.warn('UserStore不可用，允许跳转:', args.url)
        return args
      }

      if (!userStore.isLoggedIn) {
        // 阻止跳转，静默跳转到登录页
        userStore.checkLoginStatus(true)
        return false
      }

      return args // 已登录，允许跳转
    },
  })

  console.warn('路由守卫已安装')
}

/**
 * 移除全局路由守卫（用于调试或特殊情况）
 */
export function removeRouterGuard() {
  uni.removeInterceptor('navigateTo')
  uni.removeInterceptor('redirectTo')
  uni.removeInterceptor('switchTab')
  uni.removeInterceptor('reLaunch')

  console.warn('路由守卫已移除')
}
