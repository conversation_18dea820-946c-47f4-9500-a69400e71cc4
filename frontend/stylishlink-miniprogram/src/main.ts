import { createPinia } from 'pinia'
import { createSSRApp } from 'vue'
import { installRouterGuard } from '@/utils/router-guard'
import App from './App.vue'
import 'uno.css'

export function createApp() {
  const app = createSSRApp(App)
  app.use(createPinia())

  // 🔧 延迟安装路由守卫，避免在App实例初始化前就访问Store
  // 使用 nextTick 确保组件挂载完成后再安装路由守卫
  uni.nextTick(() => {
    console.warn('开始安装路由守卫...')
    installRouterGuard()
  })

  return {
    app,
  }
}
